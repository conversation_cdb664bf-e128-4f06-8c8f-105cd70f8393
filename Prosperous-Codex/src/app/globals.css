@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Accordion animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

/* Theme and Language Switcher Styles */
/* Theme Tab Switcher Styles - 3 Mode */
.theme-switch {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  align-items: center;
  gap: 0;
  width: 110px;
  height: 36px;
}

.dark .theme-switch {
  background: #262626; /* Dark gray background for theme switcher in dark mode */
  border: 1px solid rgba(75, 85, 99, 0.6);
}

.theme-icon {
  position: relative;
  z-index: var(--z-base);
  color: #6b7280;
  transition: all 0.1s ease-in-out;
  padding: 4px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(33.333%);
  height: 28px;
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
}

.theme-icon:hover {
  color: #374151;
}

.dark .theme-icon:hover {
  color: #d1d5db;
}

/* Light mode active */
.theme-switch.light .sun-icon {
  color: #5E6AD2;
  font-weight: 600;
}

/* System mode active */
.theme-switch.system .system-icon {
  color: #5E6AD2;
  font-weight: 600;
}

/* Dark mode active */
.theme-switch.dark .moon-icon {
  color: #8b93f1;
  font-weight: 600;
}

.dark .theme-icon {
  color: #9ca3af;
}

.theme-switch-toggle {
  position: absolute;
  top: 4px;
  left: 4px;
  height: calc(100% - 8px);
  width: calc(33.333% - 8px);
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  transition: all 0.15s ease-in-out;
  z-index: var(--z-base);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(229, 231, 235, 0.8);
  transform: translateX(0);
}

/* Light mode - toggle narrower and positioned to the right */
.theme-switch.light .theme-switch-toggle {
  left: 18%;
  width: calc(30% - 8px);
  transform: translateX(-50%);
}

/* System mode - toggle centered on middle icon */
.theme-switch.system .theme-switch-toggle {
  left: 50%;
  transform: translateX(-50%);
}

/* Dark mode - toggle narrower and positioned to the left */
.theme-switch.dark .theme-switch-toggle {
  background: rgba(55, 65, 81, 0.15);
  border: 1px solid rgba(75, 85, 99, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
  left: 80%;
  width: calc(30% - 8px);
  transform: translateX(-50%);
}

/* Enhanced Language Switcher Styles */
.language-switcher {
  position: relative;
  display: inline-block;
}

.language-trigger {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.625rem 1rem;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.3) 100%);
  border: 1.5px solid hsl(var(--border));
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  font-size: 0.875rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  min-width: 4rem;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.language-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.15s ease;
  pointer-events: none;
}

.language-trigger:hover {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.8) 0%, hsl(var(--muted) / 0.4) 100%);
  border-color: hsl(var(--primary) / 0.4);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.language-trigger:hover::before {
  opacity: 1;
}

.language-trigger:focus {
  outline: none;
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2), 0 4px 12px rgba(0, 0, 0, 0.15);
}

.language-current {
  font-weight: 700;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.language-chevron {
  width: 1.125rem;
  height: 1.125rem;
  transition: all 0.15s ease-in-out;
  color: hsl(var(--primary) / 0.7);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.language-chevron.open {
  transform: rotate(180deg) scale(1.1);
  color: hsl(var(--primary));
}

.language-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  min-width: 12rem;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.1) 100%);
  border: 1.5px solid hsl(var(--border));
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: var(--z-dropdown);
  overflow: hidden;
  animation: slideDown 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(12px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-0.75rem) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem 1.25rem;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  position: relative;
  overflow: hidden;
}

.language-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.language-option:hover {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.8) 0%, hsl(var(--muted) / 0.4) 100%);
  transform: translateX(4px);
}

.language-option:hover::before {
  opacity: 1;
}

.language-option.active {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.15) 0%, hsl(var(--primary) / 0.08) 100%);
  color: hsl(var(--primary));
  border-left: 3px solid hsl(var(--primary));
}

.language-option.active .language-short {
  color: hsl(var(--primary));
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.language-short {
  font-weight: 600;
  font-size: 0.875rem;
  min-width: 2rem;
  color: hsl(var(--muted-foreground));
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.2s ease;
}

.language-full {
  font-size: 0.9rem;
  color: hsl(var(--foreground));
  font-weight: 500;
  transition: all 0.2s ease;
}

.language-option.active .language-full {
  color: hsl(var(--primary));
  font-weight: 600;
}

.language-option:first-child {
  border-top-left-radius: 0.875rem;
  border-top-right-radius: 0.875rem;
}

.language-option:last-child {
  border-bottom-left-radius: 0.875rem;
  border-bottom-right-radius: 0.875rem;
}



/* Language Tab Switcher Styles */
.language-pill-switcher {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  transition: all 0.15s ease-in-out;
}

.dark .language-pill-switcher {
  background: #262626; /* Dark gray background for language switcher in dark mode */
  border: 1px solid rgba(75, 85, 99, 0.6);
}

.language-pill-option {
  position: relative;
  z-index: var(--z-base);
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.15s ease-in-out;
  white-space: nowrap;
  outline: none;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 2.5rem;
  text-align: center;
}

.language-pill-option:hover:not(.active) {
  color: #374151;
  background: rgba(156, 163, 175, 0.1);
}

.language-pill-option.active {
  color: #5E6AD2;
  font-weight: 600;
  transition: color 0.1s ease, font-weight 0.1s ease;
}

.dark .language-pill-option {
  color: #9ca3af;
}

.dark .language-pill-option:hover:not(.active) {
  color: #d1d5db;
  background: rgba(156, 163, 175, 0.1);
}

.dark .language-pill-option.active {
  color: #8b93f1;
  transition: color 0.1s ease;
}

.language-pill-indicator {
  position: absolute;
  top: 4px;
  left: 4px;
  height: calc(100% - 8px);
  width: calc(33.333% - 0.125rem);
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  transition: all 0.15s ease-in-out;
  z-index: var(--z-base);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.dark .language-pill-indicator {
  background: rgba(55, 65, 81, 0.15);
  border: 1px solid rgba(75, 85, 99, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Text transition animations */
.text-transition-out {
  animation: textFadeOut 0.15s ease-out forwards;
}

.text-transition-in {
  animation: textFadeIn 0.15s ease-in forwards;
}

@keyframes textFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-5px);
  }
}

@keyframes textFadeIn {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.sidebar-collapsed {
  width: 0;
  min-width: 0;
}

@media (max-width: 768px) {
  .sidebar {
    width: 260px;
  }
  
  .sidebar-collapsed {
    width: 260px; /* Don't collapse on mobile */
  }
}

@media (max-width: 640px) {
  .sidebar {
    width: 100%;
    max-width: 280px;
  }
  
  .sidebar-collapsed {
    width: 100%;
    max-width: 280px; /* Don't collapse on mobile */
  }
}

.scroll-hide::-webkit-scrollbar {
  display: none;
}

.scroll-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.beautiful-shadow {
  box-shadow: 0px 0px 0px 1px rgba(0,0,0,0.06), 0px 1px 1px -0.5px rgba(0,0,0,0.06), 0px 3px 3px -1.5px rgba(0,0,0,0.06), 0px 6px 6px -3px rgba(0,0,0,0.06), 0px 12px 12px -6px rgba(0,0,0,0.06), 0px 24px 24px -12px rgba(0,0,0,0.06);
}

.beautiful-shadow-dark {
  box-shadow: 0px 0px 0px 1px rgba(0,0,0,0.15), 0px 1px 1px -0.5px rgba(0,0,0,0.15), 0px 3px 3px -1.5px rgba(0,0,0,0.15), 0px 6px 6px -3px rgba(0,0,0,0.15), 0px 12px 12px -6px rgba(0,0,0,0.15), 0px 24px 24px -12px rgba(0,0,0,0.15);
}

/* Sidebar overlay for mobile */
.sidebar-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-nav);
  transition: opacity 0.15s ease;
}

.sidebar-mobile {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: var(--z-nav);
  transform: translateX(-100%);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-mobile.open {
  transform: translateX(0);
}

/* Clean Card Design System - Inspired by Reference Design */
:root {
  /* Z-Index Scale - Systematic Layering Structure */
  --z-base: 1;           /* Base content (main page elements, cards, images) */
  --z-nav: 100;          /* Navigation/sidebars (fixed navbars, side menus) */
  --z-dropdown: 200;     /* Dropdowns/tooltips (menus, popovers, tooltips) */
  --z-modal: 300;        /* Modals/drawers (modal dialogs, slide-in panels) */
  --z-toast: 400;        /* Toast notifications (snackbars, alerts) */
  --z-critical: 500;     /* Critical overlays (blocking spinners, emergency banners) */

  /* Light mode purple color scheme from reference design */
  --purple-primary-light: #5E6AD2;
  --purple-primary-light-alpha: rgba(94, 106, 210, 0.3);

  /* Dark mode purple color scheme */
  --purple-primary: #6E56CF;
  --purple-secondary: #9E8CFC;
  --purple-tertiary: #C4B5FD;
  --purple-primary-alpha: rgba(110, 86, 207, 0.5);

  /* Light mode card colors */
  --card-bg-light: white;
  --card-border-light: #f3f4f6; /* gray-100 */
  --card-content-bg-light: #F8F9FE;

  /* Dark mode card colors */
  --card-bg-dark: #1A1A1A;
  --card-border-dark: #2A2A2A;
  --card-content-bg-dark: #212121;

  /* Light mode shadows */
  --card-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --card-shadow-hover-light: 0 4px 12px rgba(0, 0, 0, 0.06);

  /* Dark mode shadows */
  --card-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  --card-shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Simplified card styling */
[data-glow] {
  transition: all 0.15s ease-in-out;
  position: relative;
}

/* Light mode - clean card styling */
:not(.dark) [data-glow] {
  background-color: var(--card-bg-light);
  border: 1px solid var(--card-border-light);
  box-shadow: var(--card-shadow-light);
  transition: all 0.15s ease-in-out;
}

:not(.dark) [data-glow]:hover {
  border-color: var(--purple-primary-light-alpha);
  box-shadow: var(--card-shadow-hover-light);
}

/* Dark mode - clean card styling */
.dark [data-glow] {
  background-color: var(--card-bg-dark);
  border: 1px solid var(--card-border-dark);
  box-shadow: var(--card-shadow);
  transition: all 0.15s ease-in-out;
}

.dark [data-glow]:hover {
  border-color: var(--purple-primary-alpha);
  box-shadow: var(--card-shadow-hover);
}

/* Clean hover effects for content */
:not(.dark) [data-glow]:hover h1,
:not(.dark) [data-glow]:hover h2,
:not(.dark) [data-glow]:hover h3,
:not(.dark) [data-glow]:hover .card-title {
  color: var(--purple-primary-light);
}

.dark [data-glow]:hover h1,
.dark [data-glow]:hover h2,
.dark [data-glow]:hover h3,
.dark [data-glow]:hover .card-title {
  color: var(--purple-secondary);
}

.dark [data-glow]:hover .card-description,
.dark [data-glow]:hover label {
  color: rgba(255, 255, 255, 0.9);
}

/* Pulse glow animation for special states */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  50% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.5), 0 0 30px hsl(var(--primary) / 0.3);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes fade-out-pulse {
  0% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  100% {
    box-shadow: 0 0 0 hsl(var(--primary) / 0);
  }
}

.fade-out-glow {
  animation: fade-out-pulse 1.5s forwards ease-out;
}

/* Animated gradient border for loading states - fast gradient shifting animation */
@keyframes gradient-border-wave {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.gradient-border-loading {
  position: relative !important;
  border-radius: 6px !important;
  padding: 3px !important;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #a855f7, #d946ef, #ec4899, #f43f5e, #6366f1) !important;
  background-size: 300% 100% !important;
  animation: gradient-border-wave 0.15s linear infinite !important;
  z-index: 1 !important;
  box-shadow: 0 0 12px rgba(99, 102, 241, 0.4) !important;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.15s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.15s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.15s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: #0F0F0F;
  --foreground: #F5F5F5;
  --card: var(--card-bg-dark);
  --card-foreground: #F5F5F5;
  --popover: var(--card-bg-dark);
  --popover-foreground: #F5F5F5;
  --primary: var(--purple-primary);
  --primary-foreground: #FFFFFF;
  --secondary: var(--card-border-dark);
  --secondary-foreground: #F5F5F5;
  --muted: var(--card-border-dark);
  --muted-foreground: #9CA3AF;
  --accent: var(--purple-secondary);
  --accent-foreground: #FFFFFF;
  --destructive: #EF4444;
  --border: var(--card-border-dark);
  --input: var(--card-border-dark);
  --ring: var(--purple-primary);
  --chart-1: var(--purple-primary);
  --chart-2: var(--purple-secondary);
  --chart-3: var(--purple-tertiary);
  --chart-4: #10B981;
  --chart-5: #F59E0B;
  --sidebar: var(--card-bg-dark);
  --sidebar-foreground: #F5F5F5;
  --sidebar-primary: var(--purple-primary);
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: var(--card-border-dark);
  --sidebar-accent-foreground: #F5F5F5;
  --sidebar-border: var(--card-border-dark);
  --sidebar-ring: var(--purple-primary);
}

/* Clean Input Field Styling - Matching Card Design System */
input,
select,
textarea,
[role="combobox"] {
  background-color: white;
  border: 1px solid var(--card-border-light);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  transition: all 0.15s ease-in-out;
  box-shadow: var(--card-shadow-light);
}

.dark input,
.dark select,
.dark textarea,
.dark [role="combobox"] {
  background-color: var(--card-bg-dark);
  border-color: var(--card-border-dark);
  color: #F5F5F5;
  box-shadow: var(--card-shadow);
}

/* Focus states with purple accent colors */
input:focus,
select:focus,
textarea:focus,
[role="combobox"]:focus {
  outline: none;
  border-color: var(--purple-primary-light);
  box-shadow:
    var(--card-shadow-hover-light),
    0 0 0 3px var(--purple-primary-light-alpha);
}

.dark input:focus,
.dark select:focus,
.dark textarea:focus,
.dark [role="combobox"]:focus {
  border-color: var(--purple-primary);
  box-shadow:
    var(--card-shadow-hover),
    0 0 0 3px var(--purple-primary-alpha);
}

/* Hover states for input fields */
input:hover:not(:focus),
select:hover:not(:focus),
textarea:hover:not(:focus),
[role="combobox"]:hover:not(:focus) {
  border-color: var(--purple-primary-light-alpha);
  box-shadow: var(--card-shadow-hover-light);
}

.dark input:hover:not(:focus),
.dark select:hover:not(:focus),
.dark textarea:hover:not(:focus),
.dark [role="combobox"]:hover:not(:focus) {
  border-color: var(--purple-primary-alpha);
  box-shadow: var(--card-shadow-hover);
}

/* Header Tabs and Brand Styles */
.brand-title {
  background: linear-gradient(135deg, #5E6AD2 0%, #9E8CFC 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.dark .brand-title {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.tabs-container {
  position: relative;
  display: flex;
  align-items: center;
}

.tabs-wrapper {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  transition: all 0.15s ease-in-out;
}

.dark .tabs-wrapper {
  background: #262626; /* Dark gray background for tab wrapper in dark mode */
  border: 1px solid rgba(75, 85, 99, 0.6);
}

.tab-item {
  position: relative;
  z-index: var(--z-base);
  padding: 10px 20px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.15s ease-in-out;
  white-space: nowrap;
  outline: none;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.tab-item:hover:not(.active) {
  color: #374151;
  background: rgba(156, 163, 175, 0.1);
}

.tab-item.active {
  color: #5E6AD2;
  font-weight: 600;
  transition: color 0.1s ease, font-weight 0.1s ease;
}

.dark .tab-item {
  color: #9ca3af;
}

.dark .tab-item:hover:not(.active) {
  color: #d1d5db;
  background: rgba(156, 163, 175, 0.1);
}

.dark .tab-item.active {
  color: #8b93f1;
  transition: color 0.1s ease;
}

.tab-indicator {
  position: absolute;
  top: 4px;
  left: 4px;
  height: calc(100% - 8px);
  background-color: rgba(255, 255, 255, 0.15); /* Semi-transparent background for light mode */
  border-radius: 8px;
  transition: all 0.15s ease-in-out;
  z-index: var(--z-base);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.dark .tab-indicator {
  background-color: rgba(64, 64, 64, 0.15); /* Semi-transparent grey background for dark mode */
  border: 1px solid rgba(75, 85, 99, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Tab content animations */
.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Language Switcher Pill Design */
/* Legacy language switcher styles completely removed */



@layer base {
  * {
    @apply border-border outline-ring/50;
    transition: background-color 0.15s ease-in-out, 
                border-color 0.15s ease-in-out, 
                color 0.15s ease-in-out, 
                fill 0.15s ease-in-out, 
                stroke 0.15s ease-in-out, 
                opacity 0.15s ease-in-out, 
                box-shadow 0.15s ease-in-out, 
                transform 0.15s ease-in-out;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* Override for elements that should not have transitions */
  .no-transition,
  .no-transition * {
    transition: none !important;
  }
  
  /* Fast transitions for interactive elements */
  button, 
  input, 
  select, 
  textarea, 
  [role="button"], 
  [tabindex]:not([tabindex="-1"]) {
    transition: background-color 0.1s ease-in-out, 
                border-color 0.1s ease-in-out, 
                color 0.1s ease-in-out, 
                box-shadow 0.1s ease-in-out, 
                transform 0.1s ease-in-out;
  }
}
